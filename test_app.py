#!/usr/bin/env python3
"""
PySide6 应用程序功能测试脚本
"""

import sys
import os
import json
import tempfile

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_functionality():
    """测试设置功能"""
    print("🧪 测试设置功能...")
    
    try:
        from main_pyside6 import ModernApp
        from PySide6.QtWidgets import QApplication
        
        # 创建临时应用实例进行测试
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建主应用实例
        main_app = ModernApp()
        
        # 测试设置加载
        print("✅ 设置加载: 成功")
        
        # 测试设置更新
        main_app.update_setting('test_key', 'test_value')
        assert main_app.app_settings['test_key'] == 'test_value'
        print("✅ 设置更新: 成功")
        
        # 测试主题应用
        main_app.apply_theme('暗色')
        print("✅ 主题切换: 成功")
        
        # 测试设置保存
        result = main_app.save_settings()
        assert result == True
        print("✅ 设置保存: 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置功能测试失败: {e}")
        return False

def test_ui_components():
    """测试 UI 组件"""
    print("🧪 测试 UI 组件...")
    
    try:
        from main_pyside6 import SettingsPage, OllamaTestPage, ModernApp
        from PySide6.QtWidgets import QApplication
        
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        main_app = ModernApp()
        
        # 测试设置页面
        settings_page = main_app.pages.get('settings')
        assert settings_page is not None
        print("✅ 设置页面创建: 成功")
        
        # 测试 Ollama 页面
        ollama_page = main_app.pages.get('ollama')
        assert ollama_page is not None
        print("✅ Ollama 页面创建: 成功")
        
        # 测试页面切换
        main_app.show_page('settings')
        main_app.show_page('ollama')
        print("✅ 页面切换: 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI 组件测试失败: {e}")
        return False

def test_style_themes():
    """测试样式主题"""
    print("🧪 测试样式主题...")
    
    try:
        from main_pyside6 import ModernApp
        from PySide6.QtWidgets import QApplication
        
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        main_app = ModernApp()
        
        # 测试所有主题
        themes = ['现代', '暗色', '经典']
        for theme in themes:
            main_app.apply_theme(theme)
            print(f"✅ {theme}主题: 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 样式主题测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("🧪 测试文件操作...")
    
    try:
        # 测试设置文件读写
        test_settings = {
            "language": "中文",
            "theme": "现代",
            "auto_save": True
        }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        # 读取测试
        with open(temp_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        assert loaded_settings == test_settings
        print("✅ JSON 文件读写: 成功")
        
        # 清理临时文件
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 PySide6 应用程序功能测试")
    print("=" * 60)
    
    tests = [
        ("文件操作", test_file_operations),
        ("设置功能", test_settings_functionality),
        ("UI 组件", test_ui_components),
        ("样式主题", test_style_themes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试: 通过")
        else:
            print(f"❌ {test_name}测试: 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

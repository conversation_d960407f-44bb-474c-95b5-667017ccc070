# 现代化桌面应用程序

基于 Tkinter 的现代化 Python 桌面应用程序，具有简洁的界面设计和可扩展的架构。

## 功能特点

- 🎨 现代化界面设计，使用 ttk 美化控件
- 📱 响应式布局，支持窗口缩放
- 🔧 模块化架构，易于扩展新功能
- ⚙️ 设置页面，支持基本配置
- 🤖 Ollama 测试页面，用于 AI 模型连接测试

## 界面布局

- **左侧边栏**: 包含 Logo、项目名称和功能按钮
- **右侧内容区**: 可切换的页面显示区域
- **现代化样式**: 使用深色侧边栏和浅色内容区的对比设计

## 运行方法

### 方法一：直接运行主程序
```bash
python main.py
```

### 方法二：使用启动脚本
```bash
python run.py
```

## 项目结构

```
cerateVideo/
├── main.py          # 主应用程序文件
├── run.py           # 启动脚本
└── README.md        # 项目说明文档
```

## 代码架构

### 主要类

1. **ModernApp**: 主应用程序类
   - 负责窗口初始化和样式配置
   - 管理侧边栏和内容区域
   - 处理页面切换逻辑

2. **BasePage**: 页面基类
   - 提供页面的基本结构
   - 定义显示/隐藏方法
   - 子类需要重写 `create_content()` 方法

3. **SettingsPage**: 设置页面
   - 继承自 BasePage
   - 提供基本的应用设置选项

4. **OllamaTestPage**: Ollama 测试页面
   - 继承自 BasePage
   - 用于测试 Ollama AI 模型连接

## 扩展新页面

要添加新页面，请按以下步骤操作：

1. 创建新的页面类，继承自 `BasePage`：
```python
class NewPage(BasePage):
    def create_content(self):
        # 在这里创建页面内容
        title = ttk.Label(self.frame, text="新页面", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))
```

2. 在 `ModernApp.create_pages()` 方法中注册新页面：
```python
self.pages["new_page"] = NewPage(self.content_area, self.colors)
```

3. 在侧边栏添加对应的按钮：
```python
new_btn = ttk.Button(menu_frame, 
                    text="🆕 新功能", 
                    style='Menu.TButton',
                    command=lambda: self.show_page("new_page"))
new_btn.pack(fill=tk.X, pady=(0, 10))
```

## 样式自定义

应用程序使用预定义的颜色方案，可以在 `setup_styles()` 方法中修改：

```python
self.colors = {
    'primary': '#2c3e50',      # 主色调
    'secondary': '#34495e',    # 次要色调
    'accent': '#3498db',       # 强调色
    'background': '#ecf0f1',   # 背景色
    'text': '#2c3e50',         # 文字色
    'white': '#ffffff',        # 白色
    'hover': '#2980b9'         # 悬停色
}
```

## 系统要求

- Python 3.6+
- tkinter (通常随 Python 一起安装)
- 支持 Windows、macOS 和 Linux

## 许可证

本项目采用 MIT 许可证。

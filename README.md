# 现代化桌面应用程序

基于 Tkinter 的现代化 Python 桌面应用程序，具有简洁的界面设计和可扩展的架构。

## 功能特点

- 🎨 现代化界面设计，使用 ttk 美化控件
- 📱 响应式布局，支持窗口缩放
- 🔧 模块化架构，易于扩展新功能
- ⚙️ 设置页面，支持基本配置
- 🤖 Ollama 测试页面，用于 AI 模型连接测试

## 界面布局

- **左侧边栏**: 包含 Logo、项目名称和功能按钮
- **右侧内容区**: 可切换的页面显示区域
- **现代化样式**: 使用深色侧边栏和浅色内容区的对比设计

## 运行方法

### 方法一：直接运行主程序
```bash
python main.py
```

### 方法二：使用启动脚本
```bash
python run.py
```

## 项目结构

```
# 视频创作工具

一个基于 PySide6 构建的现代化桌面应用程序，具有优雅的界面设计和丰富的功能。

## 🚀 特性

### 🎨 现代化界面设计
- **三种精美主题**: 现代、暗色、经典
- **响应式布局**: 支持窗口缩放和自适应
- **渐变效果**: 使用 QSS 样式表实现现代化视觉效果
- **图标支持**: 使用 Emoji 图标增强用户体验

### ⚙️ 完整的设置系统
- **实时主题切换**: 选择主题后立即应用
- **多语言支持**: 中文/英文切换
- **自动保存功能**: 可配置的自动保存
- **设置导出**: 支持导出设置到 JSON 文件
- **设置重置**: 一键恢复默认设置
- **Ollama 集成设置**: 服务器地址、默认模型配置
- **模型管理**: 刷新、删除、创建自定义模型

### 🤖 Ollama 测试功能
- **动态模型获取**: 自动从本地 Ollama 服务获取已安装的模型列表
- **智能模型选择**: 支持手动刷新和自动检测本地模型
- **多模型支持**: 兼容所有 Ollama 支持的模型（LLaMA、Gemma、CodeLlama 等）
- **智能对话**: 多行提示词输入，支持复杂对话场景
- **异步处理**: 使用 QThread 防止界面卡顿
- **实时反馈**: 进度条和状态提示，清晰的请求状态
- **格式化显示**: 美观的响应结果展示
- **错误处理**: 完善的异常处理和用户提示
- **便捷操作**: 一键清空输入/输出，智能状态管理

## 📦 安装和运行

### 环境要求
- Python 3.7+
- PySide6 6.5.0+

### 安装依赖
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install PySide6
```

### 运行应用
```bash
python main.py
```

## 🎯 使用指南

### 主界面
- **左侧边栏**: 包含 Logo、应用名称和功能菜单
- **右侧内容区**: 显示当前选中的页面内容
- **响应式设计**: 支持窗口大小调整

### 设置页面
1. **主题切换**: 在下拉框中选择主题，界面会立即应用
2. **语言设置**: 选择界面语言（中文/英文）
3. **自动保存**: 勾选启用自动保存功能
4. **Ollama 服务器**: 配置 Ollama 服务器地址
5. **默认模型**: 设置默认使用的 AI 模型
6. **模型管理**: 刷新模型列表、删除模型、创建自定义模型
7. **保存设置**: 手动保存当前设置
8. **重置默认**: 恢复所有默认设置
9. **导出设置**: 将设置导出为 JSON 文件

### Ollama 测试页面
1. **配置服务器**: 设置 Ollama 服务器地址（默认: http://localhost:11434）
2. **获取模型**: 应用启动时自动获取本地模型，也可点击 🔄 手动刷新
3. **选择模型**: 从下拉框中选择要使用的 AI 模型（显示实际安装的模型）
4. **输入提示词**: 在多行文本框中输入您的问题或指令
5. **发送请求**: 点击发送按钮，系统会异步处理请求
6. **查看响应**: 在结果区域查看格式化的模型响应
7. **管理内容**: 使用清理按钮管理输入和输出内容

## 🛠️ 技术架构

### 主要组件
- `ModernApp`: 主应用程序类，管理窗口和全局设置
- `BasePage`: 页面基类，提供统一的页面接口
- `SettingsPage`: 设置页面，处理应用配置
- `OllamaTestPage`: Ollama 测试页面

### 样式系统
- **QSS 样式表**: 使用 Qt 样式表实现现代化界面
- **主题系统**: 支持多主题切换
- **渐变效果**: 使用线性和径向渐变
- **悬停效果**: 交互式按钮和控件效果

### 设置管理
- **JSON 存储**: 设置保存在 `app_settings.json` 文件中
- **实时更新**: 设置更改立即生效
- **备份功能**: 支持设置导出和恢复

## 🔧 扩展开发

### 添加新页面
1. 继承 `BasePage` 类
2. 实现 `init_ui()` 方法
3. 在 `ModernApp.create_pages()` 中注册
4. 在侧边栏添加对应按钮

### 自定义主题
1. 在 `ModernApp` 中添加新的样式方法
2. 更新 `apply_theme()` 方法
3. 在设置页面的主题选项中添加新主题

### 添加新功能
- 扩展设置选项
- ✅ 完整的 Ollama API 集成
- ✅ 异步请求处理和多模型支持
- 添加更多工具页面
- 集成其他服务

## 📁 文件结构

```
├── main.py              # 🎯 主应用程序文件（唯一入口）
├── requirements.txt     # 📦 依赖项列表
├── README.md           # 📚 使用说明
└── app_settings.json   # ⚙️ 应用设置文件（运行后生成）
```

## 🎨 主题预览

### 现代主题
- 主色调: 深蓝灰色 (#2c3e50)
- 强调色: 蓝色 (#3498db)
- 背景: 浅灰色渐变

### 暗色主题
- 主色调: GitHub 暗色风格
- 强调色: 蓝色 (#58a6ff)
- 背景: 深色渐变

### 经典主题
- 主色调: 浅灰色
- 强调色: 微软蓝 (#0078d4)
- 背景: 白色

## 📝 版本历史

### v1.0.0 (当前版本)
- ✅ 基础界面框架
- ✅ 三种主题系统
- ✅ 完整设置功能
- ✅ 完整的 Ollama API 集成
- ✅ 异步请求处理
- ✅ 多模型支持（LLaMA、Gemma、CodeLlama 等）
- ✅ 智能错误处理
- ✅ 设置导出功能
- ✅ 单一入口文件设计

---

**开发者**: AI Assistant
**技术栈**: Python + PySide6
**许可证**: MIT/
├── main.py          # 主应用程序文件
├── run.py           # 启动脚本
└── README.md        # 项目说明文档
```

## 代码架构

### 主要类

1. **ModernApp**: 主应用程序类
   - 负责窗口初始化和样式配置
   - 管理侧边栏和内容区域
   - 处理页面切换逻辑

2. **BasePage**: 页面基类
   - 提供页面的基本结构
   - 定义显示/隐藏方法
   - 子类需要重写 `create_content()` 方法

3. **SettingsPage**: 设置页面
   - 继承自 BasePage
   - 提供基本的应用设置选项

4. **OllamaTestPage**: Ollama 测试页面
   - 继承自 BasePage
   - 用于测试 Ollama AI 模型连接

## 扩展新页面

要添加新页面，请按以下步骤操作：

1. 创建新的页面类，继承自 `BasePage`：
```python
class NewPage(BasePage):
    def create_content(self):
        # 在这里创建页面内容
        title = ttk.Label(self.frame, text="新页面", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))
```

2. 在 `ModernApp.create_pages()` 方法中注册新页面：
```python
self.pages["new_page"] = NewPage(self.content_area, self.colors)
```

3. 在侧边栏添加对应的按钮：
```python
new_btn = ttk.Button(menu_frame, 
                    text="🆕 新功能", 
                    style='Menu.TButton',
                    command=lambda: self.show_page("new_page"))
new_btn.pack(fill=tk.X, pady=(0, 10))
```

## 样式自定义

应用程序使用预定义的颜色方案，可以在 `setup_styles()` 方法中修改：

```python
self.colors = {
    'primary': '#2c3e50',      # 主色调
    'secondary': '#34495e',    # 次要色调
    'accent': '#3498db',       # 强调色
    'background': '#ecf0f1',   # 背景色
    'text': '#2c3e50',         # 文字色
    'white': '#ffffff',        # 白色
    'hover': '#2980b9'         # 悬停色
}
```

## 系统要求

- Python 3.6+
- tkinter (通常随 Python 一起安装)
- 支持 Windows、macOS 和 Linux

## 许可证

本项目采用 MIT 许可证。

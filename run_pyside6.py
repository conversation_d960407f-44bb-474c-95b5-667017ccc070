#!/usr/bin/env python3
"""
PySide6 版本的现代化桌面应用程序启动脚本
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖项"""
    try:
        import PySide6
        print(f"✓ PySide6 版本: {PySide6.__version__}")
        return True
    except ImportError:
        print("❌ 未找到 PySide6")
        print("请安装 PySide6: pip install PySide6")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("视频创作工具 - PySide6 版本")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        from main_pyside6 import main as app_main
        print("正在启动应用程序...")
        app_main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖项都已正确安装")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()

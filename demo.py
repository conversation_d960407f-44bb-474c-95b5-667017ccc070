#!/usr/bin/env python3
"""
PySide6 应用程序演示脚本
展示应用程序的主要功能和特性
"""

import sys
import os
import time

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🎬 视频创作工具 - PySide6 版本                    ║
║                                                              ║
║                    现代化桌面应用程序演示                          ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"   Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("   ❌ Python 版本过低，需要 3.7+")
        return False
    else:
        print("   ✅ Python 版本符合要求")
    
    # 检查 PySide6
    try:
        import PySide6
        print(f"   ✅ PySide6 版本: {PySide6.__version__}")
    except ImportError:
        print("   ❌ 未安装 PySide6")
        print("   💡 请运行: pip install PySide6")
        return False
    
    return True

def show_features():
    """展示功能特性"""
    print("\n🚀 应用程序特性:")
    
    features = [
        "🎨 三种精美主题 (现代/暗色/经典)",
        "⚙️ 完整的设置系统",
        "🌐 多语言支持 (中文/英文)",
        "💾 自动保存功能",
        "📤 设置导出/导入",
        "🤖 Ollama 服务测试",
        "📱 响应式界面设计",
        "🎯 模块化架构",
        "🔧 易于扩展"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"   {i:2d}. {feature}")
        time.sleep(0.1)  # 添加小延迟以增强演示效果

def show_file_structure():
    """显示文件结构"""
    print("\n📁 项目文件结构:")
    
    files = [
        ("main_pyside6.py", "主应用程序文件"),
        ("run_pyside6.py", "启动脚本"),
        ("test_app.py", "功能测试脚本"),
        ("demo.py", "演示脚本"),
        ("requirements.txt", "依赖项列表"),
        ("README_PySide6.md", "详细使用说明"),
        ("app_settings.json", "应用设置文件 (运行后生成)")
    ]
    
    for filename, description in files:
        status = "✅" if os.path.exists(filename) else "⚠️"
        print(f"   {status} {filename:<20} - {description}")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 使用指南:")
    
    steps = [
        "运行应用程序: python run_pyside6.py",
        "在左侧菜单中选择功能页面",
        "设置页面: 配置主题、语言等选项",
        "Ollama 测试: 测试 AI 服务连接",
        "所有设置自动保存到 app_settings.json"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"   {i}. {step}")

def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示:")
    print("   选择要执行的操作:")
    print("   1. 启动应用程序")
    print("   2. 运行功能测试")
    print("   3. 查看详细文档")
    print("   4. 退出")
    
    while True:
        try:
            choice = input("\n   请输入选项 (1-4): ").strip()
            
            if choice == "1":
                print("   🚀 正在启动应用程序...")
                os.system("python run_pyside6.py")
                break
                
            elif choice == "2":
                print("   🧪 正在运行功能测试...")
                os.system("python test_app.py")
                break
                
            elif choice == "3":
                print("   📚 正在打开文档...")
                if os.path.exists("README_PySide6.md"):
                    with open("README_PySide6.md", "r", encoding="utf-8") as f:
                        content = f.read()
                    print("\n" + "="*60)
                    print(content[:1000] + "..." if len(content) > 1000 else content)
                    print("="*60)
                else:
                    print("   ❌ 文档文件不存在")
                break
                
            elif choice == "4":
                print("   👋 再见！")
                break
                
            else:
                print("   ❌ 无效选项，请重新输入")
                
        except KeyboardInterrupt:
            print("\n   👋 再见！")
            break
        except Exception as e:
            print(f"   ❌ 发生错误: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 显示特性
    show_features()
    
    # 显示文件结构
    show_file_structure()
    
    # 显示使用指南
    show_usage_guide()
    
    # 交互式演示
    interactive_demo()

if __name__ == "__main__":
    main()

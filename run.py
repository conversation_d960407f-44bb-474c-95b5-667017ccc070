#!/usr/bin/env python3
"""
现代化桌面应用程序启动脚本
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main import ModernApp
    
    if __name__ == "__main__":
        print("正在启动应用程序...")
        app = ModernApp()
        app.run()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖项都已正确安装")
    sys.exit(1)
    
except Exception as e:
    print(f"应用程序启动失败: {e}")
    sys.exit(1)

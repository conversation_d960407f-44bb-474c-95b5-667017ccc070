import tkinter as tk
from tkinter import ttk
import os
import sys

class ModernApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("现代化应用程序")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置应用程序图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 配置样式
        self.setup_styles()
        
        # 创建主界面
        self.create_main_interface()
        
        # 初始化页面
        self.current_page = None
        self.pages = {}
        self.create_pages()
        
        # 显示默认页面
        self.show_page("settings")
    
    def setup_styles(self):
        """配置现代化样式"""
        style = ttk.Style()
        
        # 设置主题
        style.theme_use('clam')
        
        # 配置颜色
        self.colors = {
            'primary': '#2c3e50',      # 深蓝灰色
            'secondary': '#34495e',    # 稍浅的蓝灰色
            'accent': '#3498db',       # 蓝色
            'background': '#ecf0f1',   # 浅灰色背景
            'text': '#2c3e50',         # 深色文字
            'white': '#ffffff',        # 白色
            'hover': '#2980b9'         # 悬停颜色
        }
        
        # 配置样式
        style.configure('Sidebar.TFrame', background=self.colors['primary'])
        style.configure('Content.TFrame', background=self.colors['background'])
        style.configure('Logo.TLabel', 
                       background=self.colors['primary'], 
                       foreground=self.colors['white'],
                       font=('Arial', 16, 'bold'))
        style.configure('Title.TLabel', 
                       background=self.colors['primary'], 
                       foreground=self.colors['white'],
                       font=('Arial', 12))
        style.configure('Menu.TButton',
                       background=self.colors['secondary'],
                       foreground=self.colors['white'],
                       font=('Arial', 10),
                       borderwidth=0,
                       focuscolor='none')
        style.map('Menu.TButton',
                 background=[('active', self.colors['hover']),
                           ('pressed', self.colors['accent'])])
        
        style.configure('Page.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=('Arial', 14, 'bold'))
        
        style.configure('Content.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=('Arial', 10))
    
    def create_main_interface(self):
        """创建主界面布局"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 左侧边栏
        self.sidebar = ttk.Frame(main_container, style='Sidebar.TFrame', width=250)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # 防止框架收缩
        
        # 右侧内容区域
        self.content_area = ttk.Frame(main_container, style='Content.TFrame')
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_sidebar()
    
    def create_sidebar(self):
        """创建左侧边栏"""
        # Logo 区域
        logo_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        logo_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Logo 图标（使用文字代替）
        logo_label = ttk.Label(logo_frame, text="🎬", style='Logo.TLabel')
        logo_label.pack()
        
        # 项目名称
        title_label = ttk.Label(logo_frame, text="视频创作工具", style='Title.TLabel')
        title_label.pack(pady=(10, 0))
        
        # 分隔线
        separator = ttk.Separator(self.sidebar, orient='horizontal')
        separator.pack(fill=tk.X, padx=20, pady=20)
        
        # 菜单按钮区域
        menu_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        menu_frame.pack(fill=tk.X, padx=20)
        
        # 设置按钮
        settings_btn = ttk.Button(menu_frame, 
                                 text="⚙️ 设置", 
                                 style='Menu.TButton',
                                 command=lambda: self.show_page("settings"))
        settings_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Ollama 测试按钮
        ollama_btn = ttk.Button(menu_frame, 
                               text="🤖 Ollama 测试", 
                               style='Menu.TButton',
                               command=lambda: self.show_page("ollama"))
        ollama_btn.pack(fill=tk.X, pady=(0, 10))
        
        # 底部信息
        info_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)
        
        version_label = ttk.Label(info_frame, 
                                 text="版本 1.0.0", 
                                 style='Title.TLabel',
                                 font=('Arial', 8))
        version_label.pack()
    
    def create_pages(self):
        """创建所有页面"""
        # 设置页面
        self.pages["settings"] = SettingsPage(self.content_area, self.colors)
        
        # Ollama 测试页面
        self.pages["ollama"] = OllamaTestPage(self.content_area, self.colors)
    
    def show_page(self, page_name):
        """显示指定页面"""
        # 隐藏当前页面
        if self.current_page:
            self.current_page.hide()
        
        # 显示新页面
        if page_name in self.pages:
            self.current_page = self.pages[page_name]
            self.current_page.show()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


class BasePage:
    """页面基类"""
    def __init__(self, parent, colors):
        self.parent = parent
        self.colors = colors
        self.frame = ttk.Frame(parent, style='Content.TFrame')
        self.create_content()
    
    def create_content(self):
        """创建页面内容 - 子类需要重写此方法"""
        pass
    
    def show(self):
        """显示页面"""
        self.frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()


class SettingsPage(BasePage):
    """设置页面"""
    def create_content(self):
        # 页面标题
        title = ttk.Label(self.frame, text="设置", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))
        
        # 设置选项
        settings_frame = ttk.LabelFrame(self.frame, text="基本设置", padding=20)
        settings_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 语言设置
        lang_frame = ttk.Frame(settings_frame)
        lang_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(lang_frame, text="语言:", style='Content.TLabel').pack(side=tk.LEFT)
        lang_var = tk.StringVar(value="中文")
        lang_combo = ttk.Combobox(lang_frame, textvariable=lang_var, 
                                 values=["中文", "English"], state="readonly")
        lang_combo.pack(side=tk.RIGHT)
        
        # 主题设置
        theme_frame = ttk.Frame(settings_frame)
        theme_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(theme_frame, text="主题:", style='Content.TLabel').pack(side=tk.LEFT)
        theme_var = tk.StringVar(value="现代")
        theme_combo = ttk.Combobox(theme_frame, textvariable=theme_var,
                                  values=["现代", "经典", "暗色"], state="readonly")
        theme_combo.pack(side=tk.RIGHT)
        
        # 保存按钮
        save_btn = ttk.Button(settings_frame, text="保存设置")
        save_btn.pack(pady=(20, 0))


class OllamaTestPage(BasePage):
    """Ollama 测试页面"""
    def create_content(self):
        # 页面标题
        title = ttk.Label(self.frame, text="Ollama 测试", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))
        
        # 连接测试
        test_frame = ttk.LabelFrame(self.frame, text="连接测试", padding=20)
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 服务器地址
        server_frame = ttk.Frame(test_frame)
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(server_frame, text="服务器地址:", style='Content.TLabel').pack(side=tk.LEFT)
        server_entry = ttk.Entry(server_frame)
        server_entry.insert(0, "http://localhost:11434")
        server_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # 测试按钮
        test_btn = ttk.Button(test_frame, text="测试连接")
        test_btn.pack(pady=(10, 0))
        
        # 结果显示
        result_frame = ttk.LabelFrame(self.frame, text="测试结果", padding=20)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 结果文本框
        result_text = tk.Text(result_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)
        
        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)


if __name__ == "__main__":
    app = ModernApp()
    app.run()

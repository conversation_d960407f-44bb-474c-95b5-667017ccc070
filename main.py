import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import json

class ModernApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("现代化应用程序")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # 设置文件路径
        self.settings_file = "app_settings.json"

        # 加载设置
        self.settings = self.load_settings()

        # 设置应用程序图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 配置样式
        self.setup_styles()

        # 创建主界面
        self.create_main_interface()

        # 初始化页面
        self.current_page = None
        self.pages = {}
        self.create_pages()

        # 显示默认页面
        self.show_page("settings")

    def load_settings(self):
        """加载设置"""
        default_settings = {
            "language": "中文",
            "theme": "现代",
            "window_size": "1000x700",
            "auto_save": True
        }

        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # 合并默认设置，确保所有键都存在
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
            else:
                return default_settings
        except Exception as e:
            print(f"加载设置失败: {e}")
            return default_settings

    def save_settings(self):
        """保存设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存设置失败: {e}")
            return False

    def update_setting(self, key, value):
        """更新设置"""
        self.settings[key] = value
        self.save_settings()

    def apply_theme(self, theme_name):
        """应用主题"""
        if theme_name == "现代":
            self.colors = {
                'primary': '#2c3e50',
                'secondary': '#34495e',
                'accent': '#3498db',
                'background': '#ecf0f1',
                'text': '#2c3e50',
                'white': '#ffffff',
                'hover': '#2980b9'
            }
        elif theme_name == "暗色":
            self.colors = {
                'primary': '#1a1a1a',
                'secondary': '#2d2d2d',
                'accent': '#4a9eff',
                'background': '#121212',
                'text': '#ffffff',
                'white': '#ffffff',
                'hover': '#3d7bd8'
            }
        elif theme_name == "经典":
            self.colors = {
                'primary': '#f0f0f0',
                'secondary': '#e0e0e0',
                'accent': '#0078d4',
                'background': '#ffffff',
                'text': '#000000',
                'white': '#ffffff',
                'hover': '#106ebe'
            }

        # 重新配置样式
        self.setup_styles()

        # 刷新界面
        self.refresh_interface()

    def refresh_interface(self):
        """刷新界面以应用新主题"""
        # 这里可以添加刷新界面的逻辑
        # 由于样式已经更新，大部分控件会自动应用新样式
        pass

    def setup_styles(self):
        """配置现代化样式"""
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 如果颜色还未初始化，使用默认现代主题
        if not hasattr(self, 'colors'):
            self.apply_theme(self.settings.get('theme', '现代'))
        
        # 配置样式
        style.configure('Sidebar.TFrame', background=self.colors['primary'])
        style.configure('Content.TFrame', background=self.colors['background'])
        style.configure('Logo.TLabel', 
                       background=self.colors['primary'], 
                       foreground=self.colors['white'],
                       font=('Arial', 16, 'bold'))
        style.configure('Title.TLabel', 
                       background=self.colors['primary'], 
                       foreground=self.colors['white'],
                       font=('Arial', 12))
        style.configure('Menu.TButton',
                       background=self.colors['secondary'],
                       foreground=self.colors['white'],
                       font=('Arial', 10),
                       borderwidth=0,
                       focuscolor='none')
        style.map('Menu.TButton',
                 background=[('active', self.colors['hover']),
                           ('pressed', self.colors['accent'])])
        
        style.configure('Page.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=('Arial', 14, 'bold'))
        
        style.configure('Content.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=('Arial', 10))
    
    def create_main_interface(self):
        """创建主界面布局"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 左侧边栏
        self.sidebar = ttk.Frame(main_container, style='Sidebar.TFrame', width=250)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # 防止框架收缩
        
        # 右侧内容区域
        self.content_area = ttk.Frame(main_container, style='Content.TFrame')
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_sidebar()
    
    def create_sidebar(self):
        """创建左侧边栏"""
        # Logo 区域
        logo_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        logo_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Logo 图标（使用文字代替）
        logo_label = ttk.Label(logo_frame, text="🎬", style='Logo.TLabel')
        logo_label.pack()
        
        # 项目名称
        title_label = ttk.Label(logo_frame, text="视频创作工具", style='Title.TLabel')
        title_label.pack(pady=(10, 0))
        
        # 分隔线
        separator = ttk.Separator(self.sidebar, orient='horizontal')
        separator.pack(fill=tk.X, padx=20, pady=20)
        
        # 菜单按钮区域
        menu_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        menu_frame.pack(fill=tk.X, padx=20)
        
        # 设置按钮
        settings_btn = ttk.Button(menu_frame, 
                                 text="⚙️ 设置", 
                                 style='Menu.TButton',
                                 command=lambda: self.show_page("settings"))
        settings_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Ollama 测试按钮
        ollama_btn = ttk.Button(menu_frame, 
                               text="🤖 Ollama 测试", 
                               style='Menu.TButton',
                               command=lambda: self.show_page("ollama"))
        ollama_btn.pack(fill=tk.X, pady=(0, 10))
        
        # 底部信息
        info_frame = ttk.Frame(self.sidebar, style='Sidebar.TFrame')
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)
        
        version_label = ttk.Label(info_frame, 
                                 text="版本 1.0.0", 
                                 style='Title.TLabel',
                                 font=('Arial', 8))
        version_label.pack()
    
    def create_pages(self):
        """创建所有页面"""
        # 设置页面
        self.pages["settings"] = SettingsPage(self.content_area, self.colors, self)

        # Ollama 测试页面
        self.pages["ollama"] = OllamaTestPage(self.content_area, self.colors)
    
    def show_page(self, page_name):
        """显示指定页面"""
        # 隐藏当前页面
        if self.current_page:
            self.current_page.hide()
        
        # 显示新页面
        if page_name in self.pages:
            self.current_page = self.pages[page_name]
            self.current_page.show()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


class BasePage:
    """页面基类"""
    def __init__(self, parent, colors):
        self.parent = parent
        self.colors = colors
        self.frame = ttk.Frame(parent, style='Content.TFrame')
        self.create_content()
    
    def create_content(self):
        """创建页面内容 - 子类需要重写此方法"""
        pass
    
    def show(self):
        """显示页面"""
        self.frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def hide(self):
        """隐藏页面"""
        self.frame.pack_forget()


class SettingsPage(BasePage):
    """设置页面"""
    def __init__(self, parent, colors, app):
        self.app = app  # 保存应用实例的引用
        super().__init__(parent, colors)

    def create_content(self):
        # 页面标题
        title = ttk.Label(self.frame, text="设置", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))

        # 基本设置
        settings_frame = ttk.LabelFrame(self.frame, text="基本设置", padding=20)
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # 语言设置
        lang_frame = ttk.Frame(settings_frame)
        lang_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(lang_frame, text="语言:", style='Content.TLabel').pack(side=tk.LEFT)
        self.lang_var = tk.StringVar(value=self.app.settings.get('language', '中文'))
        lang_combo = ttk.Combobox(lang_frame, textvariable=self.lang_var,
                                 values=["中文", "English"], state="readonly", width=15)
        lang_combo.pack(side=tk.RIGHT)
        lang_combo.bind('<<ComboboxSelected>>', self.on_language_change)

        # 主题设置
        theme_frame = ttk.Frame(settings_frame)
        theme_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(theme_frame, text="主题:", style='Content.TLabel').pack(side=tk.LEFT)
        self.theme_var = tk.StringVar(value=self.app.settings.get('theme', '现代'))
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=["现代", "经典", "暗色"], state="readonly", width=15)
        theme_combo.pack(side=tk.RIGHT)
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_change)

        # 自动保存设置
        auto_save_frame = ttk.Frame(settings_frame)
        auto_save_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(auto_save_frame, text="自动保存:", style='Content.TLabel').pack(side=tk.LEFT)
        self.auto_save_var = tk.BooleanVar(value=self.app.settings.get('auto_save', True))
        auto_save_check = ttk.Checkbutton(auto_save_frame, variable=self.auto_save_var,
                                         command=self.on_auto_save_change)
        auto_save_check.pack(side=tk.RIGHT)

        # 按钮区域
        button_frame = ttk.Frame(settings_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 保存按钮
        save_btn = ttk.Button(button_frame, text="保存设置", command=self.save_settings)
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 重置按钮
        reset_btn = ttk.Button(button_frame, text="重置默认", command=self.reset_settings)
        reset_btn.pack(side=tk.LEFT)

        # 应用信息
        info_frame = ttk.LabelFrame(self.frame, text="应用信息", padding=20)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        info_text = f"""应用名称: 视频创作工具
版本: 1.0.0
设置文件: {self.app.settings_file}
当前主题: {self.app.settings.get('theme', '现代')}
语言: {self.app.settings.get('language', '中文')}"""

        self.info_label = ttk.Label(info_frame, text=info_text, style='Content.TLabel', justify=tk.LEFT)
        self.info_label.pack(anchor=tk.W)

    def on_language_change(self, event=None):
        """语言改变事件"""
        new_language = self.lang_var.get()
        self.app.update_setting('language', new_language)
        self.update_info_display()
        messagebox.showinfo("设置", f"语言已更改为: {new_language}")

    def on_theme_change(self, event=None):
        """主题改变事件"""
        new_theme = self.theme_var.get()
        self.app.update_setting('theme', new_theme)
        self.app.apply_theme(new_theme)
        self.update_info_display()
        messagebox.showinfo("设置", f"主题已更改为: {new_theme}")

    def on_auto_save_change(self):
        """自动保存设置改变事件"""
        new_auto_save = self.auto_save_var.get()
        self.app.update_setting('auto_save', new_auto_save)
        status = "启用" if new_auto_save else "禁用"
        messagebox.showinfo("设置", f"自动保存已{status}")

    def save_settings(self):
        """手动保存设置"""
        if self.app.save_settings():
            messagebox.showinfo("成功", "设置已保存！")
        else:
            messagebox.showerror("错误", "保存设置失败！")

    def reset_settings(self):
        """重置为默认设置"""
        if messagebox.askyesno("确认", "确定要重置所有设置为默认值吗？"):
            # 重置设置
            self.app.settings = {
                "language": "中文",
                "theme": "现代",
                "window_size": "1000x700",
                "auto_save": True
            }

            # 更新界面
            self.lang_var.set("中文")
            self.theme_var.set("现代")
            self.auto_save_var.set(True)

            # 应用主题
            self.app.apply_theme("现代")

            # 保存设置
            self.app.save_settings()
            self.update_info_display()

            messagebox.showinfo("成功", "设置已重置为默认值！")

    def update_info_display(self):
        """更新信息显示"""
        info_text = f"""应用名称: 视频创作工具
版本: 1.0.0
设置文件: {self.app.settings_file}
当前主题: {self.app.settings.get('theme', '现代')}
语言: {self.app.settings.get('language', '中文')}"""

        self.info_label.config(text=info_text)


class OllamaTestPage(BasePage):
    """Ollama 测试页面"""
    def create_content(self):
        # 页面标题
        title = ttk.Label(self.frame, text="Ollama 测试", style='Page.TLabel')
        title.pack(anchor=tk.W, pady=(0, 20))
        
        # 连接测试
        test_frame = ttk.LabelFrame(self.frame, text="连接测试", padding=20)
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 服务器地址
        server_frame = ttk.Frame(test_frame)
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(server_frame, text="服务器地址:", style='Content.TLabel').pack(side=tk.LEFT)
        server_entry = ttk.Entry(server_frame)
        server_entry.insert(0, "http://localhost:11434")
        server_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # 测试按钮
        test_btn = ttk.Button(test_frame, text="测试连接")
        test_btn.pack(pady=(10, 0))
        
        # 结果显示
        result_frame = ttk.LabelFrame(self.frame, text="测试结果", padding=20)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 结果文本框
        result_text = tk.Text(result_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)
        
        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)


if __name__ == "__main__":
    app = ModernApp()
    app.run()

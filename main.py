#!/usr/bin/env python3
"""
基于 PySide6 的现代化桌面应用程序
视频创作工具 - 主入口文件
"""

import sys
import json
import os
import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QStackedWidget, QComboBox,
    QCheckBox, QMessageBox, QTextEdit, QLineEdit, QGroupBox,
    QSplitter, QFileDialog
)
from PySide6.QtCore import Qt, QSettings, QTimer
from PySide6.QtGui import QFont


class ModernApp(QMainWindow):
    """主应用程序类"""

    def __init__(self):
        super().__init__()

        # 应用程序设置
        self.settings = QSettings("VideoCreator", "ModernApp")
        self.settings_file = "app_settings.json"

        # 加载设置
        self.app_settings = self.load_settings()

        # 初始化语言系统
        self.init_language_system()

        # 初始化界面
        self.init_ui()
        self.setup_styles()
        self.create_pages()

        # 显示默认页面
        self.show_page("settings")

    def init_language_system(self):
        """初始化语言系统"""
        self.texts = {
            "中文": {
                "app_title": "视频创作工具",
                "settings": "设置",
                "ollama_test": "Ollama 测试",
                "version": "版本 1.0.0",
                "basic_settings": "基本设置",
                "language": "语言:",
                "theme": "主题:",
                "auto_save": "自动保存:",
                "save_settings": "💾 保存设置",
                "reset_default": "🔄 重置默认",
                "export_settings": "📤 导出设置",
                "app_info": "应用信息",
                "app_name": "应用名称: 视频创作工具",
                "app_version": "版本: 1.0.0 (PySide6)",
                "settings_file": "设置文件:",
                "current_theme": "当前主题:",
                "current_language": "语言:",
                "auto_save_status": "自动保存:",
                "enabled": "启用",
                "disabled": "禁用",
                "connection_test": "连接测试",
                "server_address": "服务器地址:",
                "test_connection": "🔍 测试连接",
                "test_results": "测试结果",
                "testing": "测试中...",
                "retest": "🔄 重新测试",
                "language_changed": "语言已更改为:",
                "theme_changed": "主题已更改为:",
                "auto_save_enabled": "自动保存已启用",
                "auto_save_disabled": "自动保存已禁用",
                "settings_saved": "设置已保存！",
                "save_failed": "保存设置失败！",
                "confirm_reset": "确定要重置所有设置为默认值吗？",
                "reset_success": "设置已重置为默认值！",
                "export_settings_title": "导出设置",
                "export_success": "设置已导出到:",
                "export_failed": "导出设置失败:",
                "warning": "警告",
                "enter_server_address": "请输入服务器地址"
            },
            "English": {
                "app_title": "Video Creator Tool",
                "settings": "Settings",
                "ollama_test": "Ollama Test",
                "version": "Version 1.0.0",
                "basic_settings": "Basic Settings",
                "language": "Language:",
                "theme": "Theme:",
                "auto_save": "Auto Save:",
                "save_settings": "💾 Save Settings",
                "reset_default": "🔄 Reset Default",
                "export_settings": "📤 Export Settings",
                "app_info": "Application Info",
                "app_name": "App Name: Video Creator Tool",
                "app_version": "Version: 1.0.0 (PySide6)",
                "settings_file": "Settings File:",
                "current_theme": "Current Theme:",
                "current_language": "Language:",
                "auto_save_status": "Auto Save:",
                "enabled": "Enabled",
                "disabled": "Disabled",
                "connection_test": "Connection Test",
                "server_address": "Server Address:",
                "test_connection": "🔍 Test Connection",
                "test_results": "Test Results",
                "testing": "Testing...",
                "retest": "🔄 Retest",
                "language_changed": "Language changed to:",
                "theme_changed": "Theme changed to:",
                "auto_save_enabled": "Auto save enabled",
                "auto_save_disabled": "Auto save disabled",
                "settings_saved": "Settings saved!",
                "save_failed": "Failed to save settings!",
                "confirm_reset": "Are you sure you want to reset all settings to default?",
                "reset_success": "Settings have been reset to default!",
                "export_settings_title": "Export Settings",
                "export_success": "Settings exported to:",
                "export_failed": "Failed to export settings:",
                "warning": "Warning",
                "enter_server_address": "Please enter server address"
            }
        }

        # 设置当前语言
        self.current_language = self.app_settings.get('language', '中文')

    def get_text(self, key):
        """获取当前语言的文本"""
        return self.texts.get(self.current_language, self.texts["中文"]).get(key, key)

    def update_language(self, language):
        """更新语言并刷新界面"""
        self.current_language = language
        self.app_settings['language'] = language

        # 更新窗口标题
        self.setWindowTitle(self.get_text("app_title"))

        # 更新所有页面的文本
        self.refresh_all_texts()

    def refresh_all_texts(self):
        """刷新所有界面文本"""
        # 更新侧边栏文本
        self.title_label.setText(self.get_text("app_title"))
        self.settings_btn.setText(f"⚙️ {self.get_text('settings')}")
        self.ollama_btn.setText(f"🤖 {self.get_text('ollama_test')}")
        self.version_label.setText(self.get_text("version"))

        # 更新页面文本
        for page_name, page in self.pages.items():
            if hasattr(page, 'refresh_texts'):
                page.refresh_texts()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.get_text("app_title"))
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(900, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建侧边栏
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # 创建内容区域
        self.content_area = QStackedWidget()
        splitter.addWidget(self.content_area)
        
        # 设置分割器比例
        splitter.setSizes([280, 920])
        splitter.setCollapsible(0, False)
    
    def create_sidebar(self):
        """创建侧边栏"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(280)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo 区域
        logo_widget = self.create_logo_section()
        layout.addWidget(logo_widget)
        
        # 菜单区域
        menu_widget = self.create_menu_section()
        layout.addWidget(menu_widget)
        
        # 弹性空间
        layout.addStretch()
        
        # 底部信息
        info_widget = self.create_info_section()
        layout.addWidget(info_widget)
        
        return sidebar
    
    def create_logo_section(self):
        """创建 Logo 区域"""
        widget = QFrame()
        widget.setObjectName("logoSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 30, 20, 20)
        layout.setSpacing(10)
        
        # Logo 图标
        logo_label = QLabel("🎬")
        logo_label.setObjectName("logoIcon")
        logo_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(logo_label)
        
        # 应用名称
        self.title_label = QLabel(self.get_text("app_title"))
        self.title_label.setObjectName("appTitle")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)
        
        return widget
    
    def create_menu_section(self):
        """创建菜单区域"""
        widget = QFrame()
        widget.setObjectName("menuSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # 设置按钮
        self.settings_btn = QPushButton(f"⚙️ {self.get_text('settings')}")
        self.settings_btn.setObjectName("menuButton")
        self.settings_btn.clicked.connect(lambda: self.show_page("settings"))
        layout.addWidget(self.settings_btn)

        # Ollama 测试按钮
        self.ollama_btn = QPushButton(f"🤖 {self.get_text('ollama_test')}")
        self.ollama_btn.setObjectName("menuButton")
        self.ollama_btn.clicked.connect(lambda: self.show_page("ollama"))
        layout.addWidget(self.ollama_btn)
        
        return widget
    
    def create_info_section(self):
        """创建信息区域"""
        widget = QFrame()
        widget.setObjectName("infoSection")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 30)
        
        self.version_label = QLabel(self.get_text("version"))
        self.version_label.setObjectName("versionLabel")
        self.version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.version_label)
        
        return widget
    
    def load_settings(self):
        """加载应用设置"""
        default_settings = {
            "language": "中文",
            "theme": "现代",
            "window_size": "1200x800",
            "auto_save": True
        }
        
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # 合并默认设置
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
            else:
                return default_settings
        except Exception as e:
            print(f"加载设置失败: {e}")
            return default_settings
    
    def save_settings(self):
        """保存应用设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.app_settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存设置失败: {e}")
            return False
    
    def update_setting(self, key, value):
        """更新设置"""
        self.app_settings[key] = value
        if self.app_settings.get('auto_save', True):
            self.save_settings()
    
    def setup_styles(self):
        """设置应用样式"""
        theme = self.app_settings.get('theme', '现代')
        self.apply_theme(theme)
    
    def apply_theme(self, theme_name):
        """应用主题"""
        if theme_name == "现代":
            style = self.get_modern_style()
        elif theme_name == "暗色":
            style = self.get_dark_style()
        elif theme_name == "经典":
            style = self.get_classic_style()
        else:
            style = self.get_modern_style()
        
        self.setStyleSheet(style)

    def get_modern_style(self):
        """现代主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #f8f9fa;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2c3e50, stop:1 #34495e);
            border-right: 2px solid #34495e;
        }

        /* Logo 区域 */
        QFrame#logoSection {
            background: transparent;
            border-bottom: 1px solid #34495e;
        }

        QLabel#logoIcon {
            font-size: 36px;
            color: #ffffff;
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                fx:0.5, fy:0.5, stop:0 rgba(52, 152, 219, 100),
                stop:1 transparent);
            border-radius: 25px;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 18px;
            font-weight: bold;
            color: #ecf0f1;
        }

        /* 菜单区域 */
        QFrame#menuSection {
            background: transparent;
        }

        QPushButton#menuButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #34495e, stop:1 #2c3e50);
            color: #ecf0f1;
            border: 1px solid #4a6741;
            padding: 15px 20px;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
            border-color: #5dade2;
        }

        QPushButton#menuButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2980b9, stop:1 #1f618d);
            border-color: #2980b9;
        }

        /* 信息区域 */
        QFrame#infoSection {
            background: transparent;
            border-top: 1px solid #34495e;
        }

        QLabel#versionLabel {
            color: #95a5a6;
            font-size: 11px;
            font-style: italic;
        }

        /* 内容区域 */
        QStackedWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
            border-radius: 10px;
            margin: 5px;
        }

        /* 通用控件样式 */
        QComboBox {
            background-color: #ffffff;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            min-width: 120px;
        }

        QComboBox:hover {
            border-color: #3498db;
        }

        QComboBox:focus {
            border-color: #2980b9;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #7f8c8d;
            margin-right: 5px;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:hover {
            border-color: #3498db;
        }

        QCheckBox::indicator:checked {
            background-color: #3498db;
            border-color: #2980b9;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #2980b9;
        }
        """

    def get_dark_style(self):
        """暗色主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #0d1117;
            color: #f0f6fc;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #161b22, stop:1 #21262d);
            border-right: 2px solid #30363d;
        }

        QLabel#logoIcon {
            font-size: 36px;
            color: #58a6ff;
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
                fx:0.5, fy:0.5, stop:0 rgba(88, 166, 255, 50),
                stop:1 transparent);
            border-radius: 25px;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 18px;
            font-weight: bold;
            color: #f0f6fc;
        }

        QPushButton#menuButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #30363d, stop:1 #21262d);
            color: #f0f6fc;
            border: 1px solid #484f58;
            padding: 15px 20px;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #58a6ff, stop:1 #1f6feb);
            border-color: #58a6ff;
            color: #ffffff;
        }

        QLabel#versionLabel {
            color: #8b949e;
            font-size: 11px;
            font-style: italic;
        }

        QStackedWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0d1117, stop:1 #161b22);
            border-radius: 10px;
            margin: 5px;
        }

        QComboBox {
            background-color: #21262d;
            border: 2px solid #30363d;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            min-width: 120px;
            color: #f0f6fc;
        }

        QComboBox:hover {
            border-color: #58a6ff;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
            color: #f0f6fc;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #30363d;
            border-radius: 4px;
            background-color: #21262d;
        }

        QCheckBox::indicator:checked {
            background-color: #58a6ff;
            border-color: #1f6feb;
        }

        QLabel#pageTitle {
            color: #f0f6fc !important;
        }

        QGroupBox {
            color: #f0f6fc !important;
            border-color: #30363d !important;
        }

        QGroupBox::title {
            background-color: #0d1117 !important;
        }
        """

    def get_classic_style(self):
        """经典主题样式"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #f0f0f0;
        }

        /* 侧边栏 */
        QFrame#sidebar {
            background-color: #e8e8e8;
            border-right: 1px solid #d0d0d0;
        }

        /* Logo 区域 */
        QFrame#logoSection {
            background: transparent;
            border-bottom: 1px solid #d0d0d0;
        }

        QLabel#logoIcon {
            font-size: 32px;
            color: #333333;
            padding: 10px;
        }

        QLabel#appTitle {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
        }

        /* 菜单区域 */
        QFrame#menuSection {
            background: transparent;
        }

        QPushButton#menuButton {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            padding: 12px 16px;
            text-align: left;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px;
        }

        QPushButton#menuButton:hover {
            background-color: #0078d4;
            color: #ffffff;
            border-color: #0078d4;
        }

        QPushButton#menuButton:pressed {
            background-color: #106ebe;
        }

        /* 信息区域 */
        QFrame#infoSection {
            background: transparent;
            border-top: 1px solid #d0d0d0;
        }

        QLabel#versionLabel {
            color: #666666;
            font-size: 11px;
            font-style: italic;
        }

        /* 内容区域 */
        QStackedWidget {
            background-color: #ffffff;
            border-radius: 10px;
            margin: 5px;
        }

        /* 通用控件样式 - 经典版本 */
        QComboBox {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 14px;
            min-width: 120px;
            color: #333333;
        }

        QComboBox:hover {
            border-color: #0078d4;
        }

        QComboBox:focus {
            border-color: #0078d4;
            outline: none;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #666666;
            margin-right: 5px;
        }

        QCheckBox {
            font-size: 14px;
            spacing: 8px;
            color: #333333;
        }

        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #cccccc;
            border-radius: 2px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:hover {
            border-color: #0078d4;
        }

        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #106ebe;
        }

        /* 经典主题的页面标题 */
        QLabel#pageTitle {
            color: #333333 !important;
        }

        /* 经典主题的组框 */
        QGroupBox {
            color: #333333 !important;
            border-color: #cccccc !important;
        }

        QGroupBox::title {
            background-color: #ffffff !important;
        }

        /* 经典主题的文本框 */
        QLineEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px;
            font-size: 14px;
            color: #333333;
        }

        QLineEdit:focus {
            border-color: #0078d4;
        }

        QTextEdit {
            background-color: #f8f9fa;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            padding: 8px;
        }
        """

    def create_pages(self):
        """创建所有页面"""
        self.pages = {}

        # 设置页面
        self.pages["settings"] = SettingsPage(self)
        self.content_area.addWidget(self.pages["settings"])

        # Ollama 测试页面
        self.pages["ollama"] = OllamaTestPage(self)
        self.content_area.addWidget(self.pages["ollama"])

    def show_page(self, page_name):
        """显示指定页面"""
        if page_name in self.pages:
            self.content_area.setCurrentWidget(self.pages[page_name])


class BasePage(QWidget):
    """页面基类"""

    def __init__(self, app):
        super().__init__()
        self.app = app
        self.init_ui()

    def init_ui(self):
        """初始化界面 - 子类需要重写"""
        pass


class SettingsPage(BasePage):
    """设置页面"""

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.title = QLabel(self.app.get_text("settings"))
        self.title.setObjectName("pageTitle")
        self.title.setStyleSheet("""
            QLabel#pageTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.title)

        # 基本设置组
        basic_group = self.create_basic_settings_group()
        layout.addWidget(basic_group)

        # 应用信息组
        info_group = self.create_info_group()
        layout.addWidget(info_group)

        # 弹性空间
        layout.addStretch()

    def create_basic_settings_group(self):
        """创建基本设置组"""
        group = QGroupBox("基本设置")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(15)

        # 语言设置
        lang_layout = QHBoxLayout()
        lang_label = QLabel("语言:")
        lang_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(["中文", "English"])
        self.lang_combo.setCurrentText(self.app.app_settings.get('language', '中文'))
        self.lang_combo.currentTextChanged.connect(self.on_language_change)

        lang_layout.addWidget(lang_label)
        lang_layout.addStretch()
        lang_layout.addWidget(self.lang_combo)
        layout.addLayout(lang_layout)

        # 主题设置
        theme_layout = QHBoxLayout()
        theme_label = QLabel("主题:")
        theme_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["现代", "暗色", "经典"])
        self.theme_combo.setCurrentText(self.app.app_settings.get('theme', '现代'))
        self.theme_combo.currentTextChanged.connect(self.on_theme_change)

        theme_layout.addWidget(theme_label)
        theme_layout.addStretch()
        theme_layout.addWidget(self.theme_combo)
        layout.addLayout(theme_layout)

        # 自动保存设置
        auto_save_layout = QHBoxLayout()
        auto_save_label = QLabel("自动保存:")
        auto_save_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.auto_save_check = QCheckBox()
        self.auto_save_check.setChecked(self.app.app_settings.get('auto_save', True))
        self.auto_save_check.toggled.connect(self.on_auto_save_change)

        auto_save_layout.addWidget(auto_save_label)
        auto_save_layout.addStretch()
        auto_save_layout.addWidget(self.auto_save_check)
        layout.addLayout(auto_save_layout)

        # 按钮区域
        button_layout = QHBoxLayout()

        save_btn = QPushButton("💾 保存设置")
        save_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        save_btn.clicked.connect(self.save_settings)

        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        reset_btn.clicked.connect(self.reset_settings)

        export_btn = QPushButton("📤 导出设置")
        export_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        export_btn.clicked.connect(self.export_settings)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(reset_btn)
        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        return group

    def get_button_style(self, color1, color2):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
        """

    def create_info_group(self):
        """创建应用信息组"""
        group = QGroupBox("应用信息")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        info_text = f"""应用名称: 视频创作工具
版本: 1.0.0 (PySide6)
设置文件: {self.app.settings_file}
当前主题: {self.app.app_settings.get('theme', '现代')}
语言: {self.app.app_settings.get('language', '中文')}
自动保存: {'启用' if self.app.app_settings.get('auto_save', True) else '禁用'}"""

        self.info_label = QLabel(info_text)
        self.info_label.setStyleSheet("""
            QLabel {
                font-weight: normal;
                font-size: 12px;
                color: #555555;
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
        """)
        layout.addWidget(self.info_label)

        return group

    def on_language_change(self, language):
        """语言改变事件"""
        self.app.update_setting('language', language)
        self.app.update_language(language)
        self.update_info_display()
        QMessageBox.information(self, self.app.get_text("settings"),
                               f"{self.app.get_text('language_changed')} {language}")

    def on_theme_change(self, theme):
        """主题改变事件"""
        self.app.update_setting('theme', theme)
        self.app.apply_theme(theme)
        self.update_info_display()
        QMessageBox.information(self, "设置", f"主题已更改为: {theme}")

    def on_auto_save_change(self, checked):
        """自动保存设置改变事件"""
        self.app.update_setting('auto_save', checked)
        self.update_info_display()
        status = "启用" if checked else "禁用"
        QMessageBox.information(self, "设置", f"自动保存已{status}")

    def save_settings(self):
        """手动保存设置"""
        if self.app.save_settings():
            QMessageBox.information(self, "成功", "设置已保存！")
        else:
            QMessageBox.critical(self, "错误", "保存设置失败！")

    def reset_settings(self):
        """重置为默认设置"""
        reply = QMessageBox.question(self, "确认", "确定要重置所有设置为默认值吗？",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 重置设置
            self.app.app_settings = {
                "language": "中文",
                "theme": "现代",
                "window_size": "1200x800",
                "auto_save": True
            }

            # 更新界面
            self.lang_combo.setCurrentText("中文")
            self.theme_combo.setCurrentText("现代")
            self.auto_save_check.setChecked(True)

            # 应用主题
            self.app.apply_theme("现代")

            # 保存设置
            self.app.save_settings()
            self.update_info_display()

            QMessageBox.information(self, "成功", "设置已重置为默认值！")

    def export_settings(self):
        """导出设置到文件"""
        # 生成默认文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"settings_backup_{timestamp}.json"

        # 选择保存位置
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出设置", default_filename, "JSON files (*.json)")

        if filename:
            try:
                # 添加导出信息
                export_data = {
                    "export_info": {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "version": "1.0.0",
                        "app": "视频创作工具"
                    },
                    "settings": self.app.app_settings
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"设置已导出到:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出设置失败:\n{str(e)}")

    def update_info_display(self):
        """更新信息显示"""
        info_text = f"""应用名称: 视频创作工具
版本: 1.0.0 (PySide6)
设置文件: {self.app.settings_file}
当前主题: {self.app.app_settings.get('theme', '现代')}
语言: {self.app.app_settings.get('language', '中文')}
自动保存: {'启用' if self.app.app_settings.get('auto_save', True) else '禁用'}"""

        self.info_label.setText(info_text)


class OllamaTestPage(BasePage):
    """Ollama 测试页面"""

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("Ollama 测试")
        title.setObjectName("pageTitle")
        title.setStyleSheet("""
            QLabel#pageTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)

        # 连接测试组
        test_group = self.create_test_group()
        layout.addWidget(test_group)

        # 结果显示组
        result_group = self.create_result_group()
        layout.addWidget(result_group)

    def create_test_group(self):
        """创建测试组"""
        group = QGroupBox("连接测试")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(15)

        # 服务器地址
        server_layout = QHBoxLayout()
        server_label = QLabel("服务器地址:")
        server_label.setStyleSheet("font-weight: normal; font-size: 14px;")
        self.server_input = QLineEdit("http://localhost:11434")
        self.server_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        server_layout.addWidget(server_label)
        server_layout.addWidget(self.server_input)
        layout.addLayout(server_layout)

        # 测试按钮
        self.test_btn = QPushButton("🔍 测试连接")
        self.test_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.test_btn.clicked.connect(self.test_connection)
        layout.addWidget(self.test_btn)

        return group

    def create_result_group(self):
        """创建结果显示组"""
        group = QGroupBox("测试结果")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ffffff;
                border: 1px solid #34495e;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        self.result_text.setPlaceholderText("测试结果将在这里显示...")
        layout.addWidget(self.result_text)

        return group

    def test_connection(self):
        """测试连接"""
        server_url = self.server_input.text().strip()
        if not server_url:
            QMessageBox.warning(self, "警告", "请输入服务器地址")
            return

        self.result_text.clear()
        self.result_text.append(f"🔍 正在测试连接到: {server_url}")
        self.result_text.append("=" * 60)

        # 禁用测试按钮防止重复点击
        self.test_btn.setEnabled(False)
        self.test_btn.setText("测试中...")

        # 模拟异步测试
        QTimer.singleShot(2000, lambda: self.show_test_result(server_url))

    def show_test_result(self, server_url):
        """显示测试结果"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.result_text.append(f"⏰ 测试时间: {current_time}")
        self.result_text.append(f"🌐 服务器地址: {server_url}")
        self.result_text.append("")

        # 模拟不同的测试结果
        import random
        if random.choice([True, False]):
            self.result_text.append("✅ 连接状态: 成功")
            self.result_text.append("📊 响应时间: 156ms")
            self.result_text.append("🔧 服务状态: 运行中")
            self.result_text.append("📋 可用模型: llama2, codellama, mistral")
        else:
            self.result_text.append("❌ 连接状态: 失败")
            self.result_text.append("⚠️  错误信息: 连接超时或服务不可用")
            self.result_text.append("💡 建议: 检查服务器地址和端口是否正确")

        self.result_text.append("")
        self.result_text.append("=" * 60)
        self.result_text.append("📝 注意: 这是模拟测试结果，请实现实际的 Ollama API 调用")

        # 恢复测试按钮
        self.test_btn.setEnabled(True)
        self.test_btn.setText("🔄 重新测试")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("视频创作工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("VideoCreator")

    # 创建主窗口
    window = ModernApp()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()

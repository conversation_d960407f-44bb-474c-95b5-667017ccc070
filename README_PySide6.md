# 视频创作工具 - PySide6 版本

一个基于 PySide6 构建的现代化桌面应用程序，具有优雅的界面设计和丰富的功能。

## 🚀 特性

### 🎨 现代化界面设计
- **三种主题**: 现代、暗色、经典
- **响应式布局**: 支持窗口缩放和自适应
- **渐变效果**: 使用 QSS 样式表实现现代化视觉效果
- **图标支持**: 使用 Em<PERSON>ji 图标增强用户体验

### ⚙️ 完整的设置系统
- **主题切换**: 实时切换应用主题
- **语言设置**: 支持中文和英文
- **自动保存**: 可配置的自动保存功能
- **设置导出**: 支持导出设置到 JSON 文件
- **设置重置**: 一键恢复默认设置

### 🤖 Ollama 测试功能
- **连接测试**: 测试 Ollama 服务器连接
- **结果显示**: 详细的测试结果和状态信息
- **模拟测试**: 包含模拟测试功能（可扩展为实际 API 调用）

## 📦 安装和运行

### 环境要求
- Python 3.7+
- PySide6 6.5.0+

### 安装依赖
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install PySide6
```

### 运行应用
```bash
python run_pyside6.py
```

或者直接运行：
```bash
python main_pyside6.py
```

## 🎯 使用指南

### 主界面
- **左侧边栏**: 包含 Logo、应用名称和功能菜单
- **右侧内容区**: 显示当前选中的页面内容
- **响应式设计**: 支持窗口大小调整

### 设置页面
1. **主题切换**: 在下拉框中选择主题，界面会立即应用
2. **语言设置**: 选择界面语言（中文/英文）
3. **自动保存**: 勾选启用自动保存功能
4. **保存设置**: 手动保存当前设置
5. **重置默认**: 恢复所有默认设置
6. **导出设置**: 将设置导出为 JSON 文件

### Ollama 测试页面
1. **输入服务器地址**: 默认为 `http://localhost:11434`
2. **点击测试连接**: 开始连接测试
3. **查看结果**: 在结果区域查看详细的测试信息

## 🛠️ 技术架构

### 主要组件
- `ModernApp`: 主应用程序类，管理窗口和全局设置
- `BasePage`: 页面基类，提供统一的页面接口
- `SettingsPage`: 设置页面，处理应用配置
- `OllamaTestPage`: Ollama 测试页面

### 样式系统
- **QSS 样式表**: 使用 Qt 样式表实现现代化界面
- **主题系统**: 支持多主题切换
- **渐变效果**: 使用线性和径向渐变
- **悬停效果**: 交互式按钮和控件效果

### 设置管理
- **JSON 存储**: 设置保存在 `app_settings.json` 文件中
- **实时更新**: 设置更改立即生效
- **备份功能**: 支持设置导出和恢复

## 🔧 扩展开发

### 添加新页面
1. 继承 `BasePage` 类
2. 实现 `init_ui()` 方法
3. 在 `ModernApp.create_pages()` 中注册
4. 在侧边栏添加对应按钮

### 自定义主题
1. 在 `ModernApp` 中添加新的样式方法
2. 更新 `apply_theme()` 方法
3. 在设置页面的主题选项中添加新主题

### 添加新功能
- 扩展设置选项
- 实现实际的 Ollama API 调用
- 添加更多工具页面
- 集成其他服务

## 📁 文件结构

```
├── main_pyside6.py      # 主应用程序文件
├── run_pyside6.py       # 启动脚本
├── requirements.txt     # 依赖项列表
├── README_PySide6.md    # 使用说明
└── app_settings.json    # 应用设置文件（运行后生成）
```

## 🎨 主题预览

### 现代主题
- 主色调: 深蓝灰色 (#2c3e50)
- 强调色: 蓝色 (#3498db)
- 背景: 浅灰色渐变

### 暗色主题
- 主色调: GitHub 暗色风格
- 强调色: 蓝色 (#58a6ff)
- 背景: 深色渐变

### 经典主题
- 主色调: 浅灰色
- 强调色: 微软蓝 (#0078d4)
- 背景: 白色

## 🚧 待实现功能

- [ ] 实际的 Ollama API 集成
- [ ] 更多语言支持
- [ ] 插件系统
- [ ] 配置文件加密
- [ ] 自动更新功能
- [ ] 日志系统

## 📝 版本历史

### v1.0.0 (当前版本)
- ✅ 基础界面框架
- ✅ 三种主题系统
- ✅ 完整设置功能
- ✅ Ollama 测试页面
- ✅ 设置导出功能

---

**开发者**: AI Assistant  
**技术栈**: Python + PySide6  
**许可证**: MIT
